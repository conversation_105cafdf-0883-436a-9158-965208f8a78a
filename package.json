{"name": "passquan-vue3-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "element-plus": "^2.9.10", "pinia": "^3.0.2", "tailwindcss": "^4.1.7", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuetify": "^3.8.10"}, "devDependencies": {"@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "sass": "^1.89.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-vuetify": "^2.1.1", "vue-tsc": "^2.2.8"}}