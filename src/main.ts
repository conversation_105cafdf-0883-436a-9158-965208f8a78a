import { createApp } from 'vue'
import router from './router'
import App from './App.vue'

// 导入公共样式
import "./style/index.scss";
// 导入tailwindcss
import "./style/tailwind.css";

// Vuetify
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
const vuetify = createVuetify({
    components,
    directives,
})

const app = createApp(App)
app.use(router)
app.use(vuetify)
app.mount('#app')