import { createWebHistory, createRouter } from 'vue-router'

import Layout from '@/layout/index.vue'
import Home from '@/views/Home.vue'
import OrderReport from '@/views/OrderReport.vue'
import OrderReport2 from '@/views/OrderReport2.vue'
import OrderReport3 from '@/views/OrderReport3.vue'

const routes = [
  {
    path: '/',
    name: "Index",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "/home",
        name: "Home",
        component: Home
      },
      {
        path: "/order-report",
        name: "OrderReport",
        component: OrderReport
      },
      {
        path: "/order-report2",
        name: "OrderReport2",
        component: OrderReport2
      },
      {
        path: "/order-report3",
        name: "OrderReport3",
        component: OrderReport3
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router