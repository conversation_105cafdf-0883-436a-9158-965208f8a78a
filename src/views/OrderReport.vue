<template>
  <v-container fluid class="pa-4">
    <v-row>
      <v-col cols="auto" class="pt-12 pr-6">
        <v-sheet
          elevation="3"
          rounded="circle"
          height="100"
          width="100"
          class="d-flex justify-center align-center text-center"
        >
          <div>
            <div class="text-subtitle-1">均单</div>
            <div class="text-caption">Avg-order</div>
          </div>
        </v-sheet>
      </v-col>
      <v-col>
        <h1 class="text-h5 mb-4">订单管理</h1>

        <!-- Filters -->
        <v-card class="mb-6">
          <v-card-text>
            <v-row align="center" dense>
              <v-col cols="12" sm="6" md="3">
                <v-select
                  v-model="filters.brandId"
                  :items="brands"
                  item-title="name"
                  item-value="id"
                  label="当前品牌"
                  density="compact"
                  variant="outlined"
                  hide-details
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <v-text-field
                  v-model="filters.startDate"
                  label="起始"
                  type="date"
                  density="compact"
                  variant="outlined"
                  hide-details
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <v-text-field
                  v-model="filters.endDate"
                  label="结束"
                  type="date"
                  density="compact"
                  variant="outlined"
                  hide-details
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="8">
                <v-chip-group
                  v-model="filters.status"
                  column
                  multiple
                >
                  <v-chip filter variant="outlined" v-for="status in orderStatuses" :key="status" :value="status">
                    {{ status }}
                  </v-chip>
                </v-chip-group>
              </v-col>
            </v-row>
            <v-row align="center" dense class="mt-2">
              <v-col cols="12" sm="4" md="2">
                <v-text-field
                  v-model="filters.source"
                  label="来源"
                  density="compact"
                  variant="outlined"
                  hide-details
                  placeholder="FOODSUP/FOODHWY"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="4" md="2">
                <v-text-field
                  v-model="filters.filter"
                  label="过滤"
                  density="compact"
                  variant="outlined"
                  hide-details
                  placeholder="由单号"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="4" md="4">
                <v-btn color="primary" class="mr-2">
                  <v-icon start>mdi-refresh</v-icon>
                  查询 Refresh
                </v-btn>
                <v-checkbox
                  v-model="filters.autoRefresh"
                  label="自动刷新 Auto refresh"
                  density="compact"
                  hide-details
                  class="d-inline-flex"
                ></v-checkbox>
                <v-checkbox
                  v-model="filters.showDetails"
                  label="显示明细"
                  density="compact"
                  hide-details
                  class="d-inline-flex"
                ></v-checkbox>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Order Cards -->
        <div v-for="order in orders" :key="order.id" class="mb-5">
          <v-card>
            <v-card-title class="d-flex align-center text-body-2 pa-2 grey lighten-4">
              <strong class="mr-2">#{{ order.id }}</strong>
              <span class="mr-4">{{ order.title }}</span>
              <v-chip small :color="order.version.includes('2.2') ? 'green' : 'orange'" text-color="white" class="mr-2">{{ order.version }}</v-chip>
              <v-spacer></v-spacer>
              <span>商家: {{ order.store.name }}, 电话: {{ order.store.phone }}, 地址: {{ order.store.address }}</span>
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <v-row>
                <!-- Left Panel -->
                <v-col cols="12" md="6">
                  <p><strong>单号:</strong> {{ order.orderNumber }} <v-icon small>mdi-content-copy</v-icon></p>
                  <p><strong>来源:</strong> {{ order.source }}</p>
                  <p><strong>收款负责人:</strong> {{ order.recipient }} <v-chip small color="blue-grey" class="ml-2 white--text">修改</v-chip></p>
                  <p><strong>下单时间:</strong> {{ order.orderTime }} <strong>预计送达时间:</strong> {{ order.deliveryTime }}</p>
                  <v-btn-group dense class="mt-4">
                    <v-btn small>客诉记录</v-btn>
                    <v-btn small>添加客诉</v-btn>
                    <v-btn small>发送Invoice</v-btn>
                    <v-btn small>发送EMT</v-btn>
                  </v-btn-group>
                  <v-textarea
                    label="内部备注"
                    :value="order.internalNote"
                    rows="2"
                    class="mt-4"
                    variant="outlined"
                    readonly
                  ></v-textarea>
                </v-col>

                <!-- Right Panel -->
                <v-col cols="12" md="6">
                  <p class="font-weight-bold">[中] 会员编号: {{ order.store.memberId }}, 第{{order.store.orderCount}}次下单</p>
                  <v-simple-table dense>
                    <template v-slot:default>
                      <thead>
                        <tr>
                          <th class="text-left">项目</th>
                          <th class="text-left">数量</th>
                          <th class="text-right">金额</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="item in order.items" :key="item.name">
                          <td>{{ item.name }}</td>
                          <td>x {{ item.qty }}</td>
                          <td class="text-right">${{ item.price.toFixed(2) }}</td>
                        </tr>
                      </tbody>
                    </template>
                  </v-simple-table>
                  <v-divider class="my-2"></v-divider>
                  <div class="d-flex justify-space-between text-body-2"><span>卖的小计</span><span>${{ order.totals.subtotal.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between text-body-2"><span>卖的小计 (税后)</span><span>${{ order.totals.subtotalWithTax.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between text-body-2"><span>配送服务费</span><span>${{ order.totals.deliveryFee.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between text-body-2"><span>司机收费</span><span>${{ order.totals.driverFee.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between text-body-2"><span>小费</span><span>${{ order.totals.tip.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between text-body-2"><span>押金</span><span>${{ order.totals.deposit.toFixed(2) }}</span></div>
                  <div class="d-flex justify-space-between font-weight-bold mt-1">
                    <span>总计</span>
                    <span>共{{ order.items.reduce((acc, item) => acc + item.qty, 0) }}份</span>
                    <span>总计 ${{ order.totals.total.toFixed(2) }}</span>
                  </div>
                  <v-textarea label="减免备注" rows="2" class="mt-4" variant="outlined"></v-textarea>
                  <v-textarea label="后台订单备注" rows="2" class="mt-2" variant="outlined"></v-textarea>
                  <v-btn color="primary" class="float-right">提交</v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </div>

        <!-- Pagination -->
        <v-pagination
          v-model="page"
          :length="10"
          rounded="circle"
        ></v-pagination>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const filters = ref({
  brandId: 821,
  startDate: '2025-06-19',
  endDate: '2025-06-19',
  status: ['处理中'],
  source: '',
  filter: '',
  autoRefresh: false,
  showDetails: false,
});

const brands = ref([
  { id: 821, name: '821: Foodhwy' },
  { id: 822, name: '822: Another Brand' },
]);

const orderStatuses = ref([
  '处理中', '所有', '新订单', '已确认', '已配送', '已完成', '已取消', '预订单', '自定义'
]);

const page = ref(1);

const orders = ref([
  {
    id: 5,
    title: '某的测试小铺 (测试供应链, 勿动)',
    version: 'Ver: 2.1.5 [200+分钟未接单,中,英]',
    store: {
      name: 'Northern Store',
      phone: '18887786666',
      address: 'Baker Lake, NU X0C 0A0, Canada',
      memberId: '01527424',
      orderCount: 27,
    },
    orderNumber: '23714443',
    source: 'FOODSUP',
    recipient: '司机',
    orderTime: '06/18 02:41',
    deliveryTime: '03:57 - 04:13',
    internalNote: '商家: test111',
    items: [
      { name: '测试WMS商品同步123', qty: 5, price: 150.00 },
    ],
    totals: {
      subtotal: 750.00,
      subtotalWithTax: 862.31,
      deliveryFee: 0.00,
      serviceFee: 3.90,
      driverFee: 0.00,
      tip: 112.31,
      deposit: 0.00,
      total: 866.21,
    },
  },
  {
    id: 2,
    title: 'FoodsUp',
    version: 'Ver: 2.2.6',
    store: {
      name: 'Scarborough Warehouse',
      phone: '14378867845',
      address: '55 Milne Ave, Scarborough, ON M1L 1K1, Canada',
      memberId: '01702644',
      orderCount: 3,
    },
    orderNumber: '23714442',
    source: 'FOODSUP',
    recipient: '司机',
    orderTime: '06/18 02:26',
    deliveryTime: '03:42 - 03:58',
    internalNote: '商家备注: 商家...',
    items: [
      { name: '测试运费模板 | 东西', qty: 5, price: 80.00 },
    ],
    totals: {
      subtotal: 400.00,
      subtotalWithTax: 452.00,
      deliveryFee: 0.00,
      serviceFee: 0,
      driverFee: 52.00,
      tip: 0,
      deposit: 0.00,
      total: 452.00,
    },
  },
  {
    id: 1,
    title: 'FoodsUp',
    version: 'Ver: 2.2.6',
    store: {
      name: 'Scarborough Warehouse',
      phone: '14378867845',
      address: '55 Milne Ave, Scarborough, ON M1L 1K1, Canada',
      memberId: '01702644',
      orderCount: 3,
    },
    orderNumber: '23714441',
    source: 'FOODSUP',
    recipient: '司机',
    orderTime: '06/18 02:17',
    deliveryTime: '03:33 - 03:49',
    internalNote: '司机备注: 司机...',
    items: [
      { name: '[ADM-202] Monarch 色拉油 20kg', qty: 1, price: 16.99 },
      { name: '[ADM-202] Monarch Pastry Flour 20 kg', qty: 1, price: 16.99 },
      { name: '罗马番茄 25 lb (新鲜采摘)', qty: 2, price: 566.35 },
    ],
    totals: {
      subtotal: 1183.67,
      subtotalWithTax: 1183.67,
      deliveryFee: 0.00,
      serviceFee: 0,
      driverFee: 0.00,
      tip: 0,
      deposit: -56.35,
      total: 1177.32,
    },
  },
]);

</script>

<style lang="scss" scoped>
.v-card-title {
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.v-btn-group .v-btn {
  border-color: rgba(0,0,0,0.12);
}

.text-body-2 {
  font-size: 0.875rem !important;
}
</style>
