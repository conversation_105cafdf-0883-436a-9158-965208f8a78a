<template>
  <v-container fluid class="pa-4">
    <!-- 品牌选择器 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card class="pa-4">
          <v-row align="center">
            <v-col cols="auto">
              <span class="text-h6">当前品牌：</span>
            </v-col>
            <v-col cols="auto">
              <v-select
                v-model="selectedBrandId"
                :items="brands"
                item-title="name"
                item-value="id"
                variant="outlined"
                density="compact"
                style="min-width: 200px"
                @update:model-value="onBrandChange"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <!-- 订单列表 -->
    <v-row>
      <v-col cols="12">
        <div v-for="order in orders" :key="order.id" class="mb-4">
          <v-card elevation="2">
            <!-- 订单头部信息 -->
            <v-card-title class="d-flex justify-space-between align-center pa-4 bg-blue-grey-50">
              <div class="d-flex align-center">
                <v-chip
                  :color="getStatusColor(order.status)"
                  variant="elevated"
                  class="me-3"
                >
                  {{ order.statusText }}
                </v-chip>
                <span class="text-h6">#{{ order.orderNumber }}</span>
              </div>
              <div class="text-caption text-grey-600">
                订单时间：{{ formatDate(order.orderTime) }}
              </div>
            </v-card-title>

            <v-divider />

            <v-card-text class="pa-0">
              <v-row no-gutters>
                <!-- 左侧：订单详情 -->
                <v-col cols="12" md="7" class="pa-4">
                  <!-- 餐厅信息 -->
                  <div class="mb-3">
                    <div class="d-flex align-center mb-2">
                      <v-icon color="primary" class="me-2">mdi-store</v-icon>
                      <span class="text-h6">{{ order.restaurant.name }}</span>
                    </div>
                    <div class="text-caption text-grey-600 ms-8">
                      地址：{{ order.restaurant.address }}
                    </div>
                    <div class="text-caption text-grey-600 ms-8">
                      电话：{{ order.restaurant.phone }}
                    </div>
                  </div>

                  <v-divider class="my-3" />

                  <!-- 配送信息 -->
                  <div class="mb-3">
                    <div class="d-flex align-center mb-2">
                      <v-icon color="success" class="me-2">mdi-truck-delivery</v-icon>
                      <span class="font-weight-medium">配送信息</span>
                    </div>
                    <div class="text-body-2 ms-8">
                      <div>收货人：{{ order.delivery.customerName }}</div>
                      <div>联系电话：{{ order.delivery.phone }}</div>
                      <div>配送地址：{{ order.delivery.address }}</div>
                      <div v-if="order.delivery.note">备注：{{ order.delivery.note }}</div>
                    </div>
                  </div>

                  <v-divider class="my-3" />

                  <!-- 商品列表 -->
                  <div class="mb-3">
                    <div class="d-flex align-center mb-2">
                      <v-icon color="orange" class="me-2">mdi-food</v-icon>
                      <span class="font-weight-medium">订单商品</span>
                    </div>
                    <v-table density="compact" class="ms-8">
                      <thead>
                        <tr>
                          <th>商品名称</th>
                          <th>数量</th>
                          <th>单价</th>
                          <th>小计</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="item in order.items" :key="item.id">
                          <td>
                            {{ item.name }}
                            <div v-if="item.specifications" class="text-caption text-grey-600">
                              {{ item.specifications }}
                            </div>
                          </td>
                          <td>{{ item.quantity }}</td>
                          <td>¥{{ item.price.toFixed(2) }}</td>
                          <td>¥{{ (item.quantity * item.price).toFixed(2) }}</td>
                        </tr>
                      </tbody>
                    </v-table>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="d-flex justify-start gap-2 mt-4">
                    <v-btn
                      color="primary"
                      variant="outlined"
                      size="small"
                      @click="viewOrderDetail(order.id)"
                    >
                      查看详情
                    </v-btn>
                    <v-btn
                      color="success"
                      variant="outlined"
                      size="small"
                      @click="contactCustomer(order.delivery.phone)"
                    >
                      联系客户
                    </v-btn>
                    <v-btn
                      color="info"
                      variant="outlined"
                      size="small"
                      @click="printOrder(order.id)"
                    >
                      打印订单
                    </v-btn>
                    <v-btn
                      v-if="order.status === 'pending'"
                      color="orange"
                      variant="outlined"
                      size="small"
                      @click="confirmOrder(order.id)"
                    >
                      确认订单
                    </v-btn>
                  </div>
                </v-col>

                <!-- 右侧：价格汇总 -->
                <v-col cols="12" md="5" class="pa-4 bg-grey-50">
                  <v-card variant="outlined" class="pa-3">
                    <v-card-title class="text-h6 pa-0 mb-3">
                      订单金额
                    </v-card-title>
                    
                    <div class="d-flex justify-space-between mb-2">
                      <span>商品金额：</span>
                      <span>¥{{ order.pricing.subtotal.toFixed(2) }}</span>
                    </div>
                    
                    <div class="d-flex justify-space-between mb-2">
                      <span>配送费：</span>
                      <span>¥{{ order.pricing.deliveryFee.toFixed(2) }}</span>
                    </div>
                    
                    <div v-if="order.pricing.discount > 0" class="d-flex justify-space-between mb-2 text-success">
                      <span>优惠金额：</span>
                      <span>-¥{{ order.pricing.discount.toFixed(2) }}</span>
                    </div>
                    
                    <div v-if="order.pricing.tax > 0" class="d-flex justify-space-between mb-2">
                      <span>税费：</span>
                      <span>¥{{ order.pricing.tax.toFixed(2) }}</span>
                    </div>
                    
                    <v-divider class="my-2" />
                    
                    <div class="d-flex justify-space-between text-h6 font-weight-bold">
                      <span>总计：</span>
                      <span class="text-primary">¥{{ order.pricing.total.toFixed(2) }}</span>
                    </div>

                    <v-divider class="my-3" />

                    <!-- 支付信息 -->
                    <div class="mb-2">
                      <div class="font-weight-medium mb-1">支付方式</div>
                      <v-chip
                        :color="getPaymentMethodColor(order.payment.method)"
                        variant="outlined"
                        size="small"
                      >
                        {{ order.payment.methodText }}
                      </v-chip>
                    </div>

                    <div v-if="order.payment.transactionId" class="mb-2">
                      <div class="font-weight-medium mb-1">交易号</div>
                      <div class="text-caption">{{ order.payment.transactionId }}</div>
                    </div>

                    <div class="mb-2">
                      <div class="font-weight-medium mb-1">支付状态</div>
                      <v-chip
                        :color="getPaymentStatusColor(order.payment.status)"
                        variant="elevated"
                        size="small"
                      >
                        {{ order.payment.statusText }}
                      </v-chip>
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>

    <!-- 分页 -->
    <v-row class="mt-4">
      <v-col cols="12" class="d-flex justify-center">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="7"
          @update:model-value="loadOrders"
        />
      </v-col>
    </v-row>

    <!-- 加载状态 -->
    <v-overlay v-model="loading" class="align-center justify-center">
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const loading = ref(false)
const selectedBrandId = ref('821')
const currentPage = ref(1)
const totalPages = ref(1)

// 品牌数据
const brands = ref([
  { id: '821', name: 'Foodhwy' },
  { id: '822', name: 'Brand A' },
  { id: '823', name: 'Brand B' }
])

// 订单数据
const orders = ref([
  {
    id: '1001',
    orderNumber: 'ORD202506190001',
    status: 'confirmed',
    statusText: '已确认',
    orderTime: '2025-06-19 14:30:25',
    restaurant: {
      name: 'Superb Food Westside',
      address: '123 Main Street, Westside',
      phone: '******-567-8900'
    },
    delivery: {
      customerName: '张三',
      phone: '******-567-8901',
      address: '456 Oak Avenue, Suite 100',
      note: '请送到前台'
    },
    items: [
      {
        id: 1,
        name: 'Crispy Chicken Burger',
        specifications: '辣味，去番茄',
        quantity: 2,
        price: 12.99
      },
      {
        id: 2,
        name: 'French Fries',
        specifications: '大份',
        quantity: 1,
        price: 4.99
      },
      {
        id: 3,
        name: 'Coca Cola',
        specifications: '500ml',
        quantity: 2,
        price: 2.99
      }
    ],
    pricing: {
      subtotal: 39.95,
      deliveryFee: 3.99,
      discount: 5.00,
      tax: 3.19,
      total: 42.13
    },
    payment: {
      method: 'card',
      methodText: '信用卡',
      status: 'paid',
      statusText: '已支付',
      transactionId: 'TXN123456789'
    }
  },
  {
    id: '1002',
    orderNumber: 'ORD202506190002',
    status: 'pending',
    statusText: '待确认',
    orderTime: '2025-06-19 15:15:10',
    restaurant: {
      name: 'Pizza Palace',
      address: '789 Elm Street, Downtown',
      phone: '******-567-8902'
    },
    delivery: {
      customerName: '李四',
      phone: '******-567-8903',
      address: '321 Pine Road, Apt 205',
      note: ''
    },
    items: [
      {
        id: 4,
        name: 'Margherita Pizza',
        specifications: '12寸，薄底',
        quantity: 1,
        price: 18.99
      },
      {
        id: 5,
        name: 'Caesar Salad',
        specifications: '不要洋葱',
        quantity: 1,
        price: 8.99
      }
    ],
    pricing: {
      subtotal: 27.98,
      deliveryFee: 2.99,
      discount: 0,
      tax: 2.48,
      total: 33.45
    },
    payment: {
      method: 'cash',
      methodText: '现金',
      status: 'pending',
      statusText: '待支付',
      transactionId: null
    }
  }
])

// 计算属性和方法
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'orange',
    confirmed: 'blue',
    preparing: 'purple',
    ready: 'green',
    delivered: 'success',
    cancelled: 'error'
  }
  return colors[status] || 'grey'
}

const getPaymentMethodColor = (method: string) => {
  const colors: Record<string, string> = {
    card: 'primary',
    cash: 'success',
    alipay: 'blue',
    wechat: 'green'
  }
  return colors[method] || 'grey'
}

const getPaymentStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    paid: 'success',
    pending: 'warning',
    failed: 'error',
    refunded: 'info'
  }
  return colors[status] || 'grey'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 事件处理方法
const onBrandChange = (brandId: string) => {
  console.log('品牌切换:', brandId)
  loadOrders()
}

const loadOrders = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const viewOrderDetail = (orderId: string) => {
  console.log('查看订单详情:', orderId)
  // 这里可以导航到订单详情页面或打开详情弹窗
}

const contactCustomer = (phone: string) => {
  console.log('联系客户:', phone)
  // 这里可以打开电话应用或发送短信
}

const printOrder = (orderId: string) => {
  console.log('打印订单:', orderId)
  // 这里可以调用打印功能
}

const confirmOrder = (orderId: string) => {
  console.log('确认订单:', orderId)
  // 这里可以调用确认订单的API
  const order = orders.value.find(o => o.id === orderId)
  if (order) {
    order.status = 'confirmed'
    order.statusText = '已确认'
  }
}

// 生命周期
onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.v-table {
  background: transparent;
}

.v-table th {
  font-weight: 600 !important;
}

.gap-2 {
  gap: 8px;
}
</style>
