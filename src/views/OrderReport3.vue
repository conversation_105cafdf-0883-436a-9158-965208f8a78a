<template>
  <v-container fluid>
    <v-card class="mb-4">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="4">
            <v-select
              label="当前品牌"
              :items="['821: Foodhwy']"
              variant="outlined"
              dense
              hide-details
            ></v-select>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-card>
      <v-card-title>
        订单
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="4">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-bind="props"
                  label="开始日期"
                  variant="outlined"
                  dense
                  hide-details
                ></v-text-field>
              </template>
              <v-date-picker></v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="12" md="4">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-bind="props"
                  label="结束日期"
                  variant="outlined"
                  dense
                  hide-details
                ></v-text-field>
              </template>
              <v-date-picker></v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="12" md="4">
            <v-checkbox label="24小时内的订单"></v-checkbox>
          </v-col>
        </v-row>
        <v-radio-group inline>
          <v-radio label="处理中" value="processing"></v-radio>
          <v-radio label="所有" value="all"></v-radio>
          <v-radio label="新订单" value="new"></v-radio>
          <v-radio label="已确认" value="confirmed"></v-radio>
          <v-radio label="已配送" value="delivered"></v-radio>
          <v-radio label="已完成" value="finished"></v-radio>
          <v-radio label="已取消" value="cancelled"></v-radio>
          <v-radio label="预订单" value="pre-order"></v-radio>
        </v-radio-group>
        <v-row>
          <v-col cols="12" md="4">
            <v-text-field
              label="来源"
              variant="outlined"
              dense
              hide-details
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              label="过滤"
              variant="outlined"
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-btn color="primary" class="mt-4">查询</v-btn>
        <v-checkbox label="自动刷新"></v-checkbox>
      </v-card-text>
    </v-card>

    <v-card v-for="order in orders" :key="order.id" class="mt-4">
      <v-card-title>
        #{{ order.id }}
        <v-chip small :color="order.version.color" class="ml-2">{{ order.version.name }}</v-chip>
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <p><strong>商家电话:</strong> {{ order.seller_phone }}</p>
            <p><strong>司机电话:</strong> {{ order.driver_phone }}</p>
            <p><strong>单号:</strong> {{ order.order_number }}</p>
            <p><strong>来源:</strong> {{ order.source }}</p>
            <p><strong>收款负责人:</strong> {{ order.collector }}</p>
            <p><strong>下单时间:</strong> {{ order.order_time }}</p>
            <p><strong>预计送达时间:</strong> {{ order.delivery_time }}</p>
          </v-col>
          <v-col cols="12" md="6">
            <p><strong>商家:</strong> {{ order.store.name }}</p>
            <p><strong>电话:</strong> {{ order.store.phone }}</p>
            <p><strong>地址:</strong> {{ order.store.address }}</p>
            <p><strong>[中]会员编号:</strong> {{ order.store.member_id }}</p>
          </v-col>
        </v-row>
        <v-table>
          <thead>
            <tr>
              <th>项目</th>
              <th>数量</th>
              <th>金额</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in order.items" :key="item.name">
              <td>{{ item.name }}</td>
              <td>{{ item.quantity }}</td>
              <td>{{ item.price }}</td>
            </tr>
          </tbody>
        </v-table>
        <v-row>
          <v-col>
            <v-textarea label="减免备注"></v-textarea>
          </v-col>
          <v-col>
            <v-textarea label="后台订单备注"></v-textarea>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup>
import { ref } from 'vue';

const orders = ref([
  {
    id: 5,
    version: { name: 'Ver: 2.1.5', color: 'green' },
    seller_phone: '9999999999',
    driver_phone: '9991111111',
    order_number: '23714443',
    source: 'FOODSUP',
    collector: '司机',
    order_time: '06/18 02:41',
    delivery_time: '03:57 - 04:13',
    store: {
      name: 'Northern Store',
      phone: '18887786666',
      address: 'Baker Lake, NU X0C 0A0, Canada',
      member_id: '01527424, 第27次下单',
    },
    items: [
      { name: '测试WMS商品同步123', quantity: 'x 5', price: '$150.00' },
      { name: '堂食的小计', quantity: '', price: '$750.00' },
      { name: '堂食的小计(税后)', quantity: '', price: '$862.31' },
    ],
  },
  // Add other orders here based on the screenshot
]);
</script>
