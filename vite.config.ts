import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { codeInspectorPlugin } from 'code-inspector-plugin'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  return {
    plugins: [
      vue(),
      AutoImport({
        resolvers: [VantResolver()]
      }),
      Components({
        resolvers: [VantResolver()]
      }),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [fileURLToPath(new URL('./src/assets/svgs', import.meta.url))],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]'
      }),
      // codeInspectorPlugin({
      //   bundler: 'vite',
      //   showSwitch: false
      // })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api', 'color-functions'],
          additionalData: `@import "@/assets/styles/global.scss";`
        }
      }
    },
    esbuild: {
      pure: ['console.log']
    },
    server: {
      proxy: {
        // '/api': {
        //   target: 'https://faddev.foodsup.com',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/api/, '')
        // }
        [env.VITE_APP_BASE_URL_DEV1]: {
          target: env.VITE_APP_API_URL_DEV1,
          secure: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_BASE_URL_DEV1}`), ''),
          configure: (proxy, options) => {
            proxy.on('proxyRes', (proxyRes, req) => {
              proxyRes.headers['x-real-url'] = new URL(req.url || '', options.target as string)?.href || '';
            });
          }
        },
        [env.VITE_APP_BASE_URL_DEV2]: {
          target: env.VITE_APP_API_URL_DEV2,
          secure: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_BASE_URL_DEV2}`), ''),
          configure: (proxy, options) => {
            proxy.on('proxyRes', (proxyRes, req) => {
              proxyRes.headers['x-real-url'] = new URL(req.url || '', options.target as string)?.href || '';
            });
          }
        }
      }
    }
  }
})
